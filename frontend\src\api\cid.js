// frontend/src/api/cid.js
/**
 * CID规则管理API接口
 */
import axios from 'axios'

export const cidApi = {
  // ==================== 主规则API ====================
  
  /**
   * 获取主规则列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.per_page - 每页数量
   * @param {string} params.search - 搜索关键词
   */
  getMainRules(params = {}) {
    return axios.get('/api/cid/main-rules', { params })
      .then(response => response.data)
  },

  /**
   * 创建主规则
   * @param {Object} data - 主规则数据
   */
  createMainRule(data) {
    return axios.post('/api/cid/main-rules', data)
      .then(response => response.data)
  },

  /**
   * 获取单个主规则详情
   * @param {number} id - 主规则ID
   */
  getMainRule(id) {
    return axios.get(`/api/cid/main-rules/${id}`)
      .then(response => response.data)
  },

  /**
   * 更新主规则
   * @param {number} id - 主规则ID
   * @param {Object} data - 更新数据
   */
  updateMainRule(id, data) {
    return axios.put(`/api/cid/main-rules/${id}`, data)
      .then(response => response.data)
  },

  /**
   * 删除主规则
   * @param {number} id - 主规则ID
   */
  deleteMainRule(id) {
    return axios.delete(`/api/cid/main-rules/${id}`)
      .then(response => response.data)
  },

  // ==================== 变体规则API ====================

  /**
   * 获取主规则的变体规则列表
   * @param {number} mainRuleId - 主规则ID
   */
  getVariantRules(mainRuleId) {
    return axios.get(`/api/cid/main-rules/${mainRuleId}/variants`)
      .then(response => response.data)
  },

  /**
   * 为主规则添加变体规则
   * @param {number} mainRuleId - 主规则ID
   * @param {Object} data - 变体规则数据
   */
  createVariantRule(mainRuleId, data) {
    return axios.post(`/api/cid/main-rules/${mainRuleId}/variants`, data)
      .then(response => response.data)
  },

  /**
   * 更新变体规则
   * @param {number} id - 变体规则ID
   * @param {Object} data - 更新数据
   */
  updateVariantRule(id, data) {
    return axios.put(`/api/cid/variant-rules/${id}`, data)
      .then(response => response.data)
  },

  /**
   * 删除变体规则
   * @param {number} id - 变体规则ID
   */
  deleteVariantRule(id) {
    return axios.delete(`/api/cid/variant-rules/${id}`)
      .then(response => response.data)
  },

  /**
   * 调整变体规则优先级顺序
   * @param {Object} data - 排序数据
   * @param {Array} data.variant_orders - 变体规则顺序数组
   */
  reorderVariantRules(data) {
    return axios.put('/api/cid/variant-rules/reorder', data)
      .then(response => response.data)
  },

  /**
   * 修复变体规则优先级
   * @param {number} mainRuleId - 主规则ID
   */
  fixVariantPriorities(mainRuleId) {
    return axios.post(`/api/cid/main-rules/${mainRuleId}/variants/fix-priorities`)
      .then(response => response.data)
  },

  /**
   * 获取数据库状态
   */
  getDatabaseStatus() {
    return axios.get('/api/cid/database/status')
      .then(response => response.data)
  },

  // ==================== CID生成API ====================

  /**
   * 根据番号和制作商生成CID
   * @param {Object} data - 生成参数
   * @param {string} data.bangou - 番号
   * @param {string} data.maker - 制作商（可选）
   */
  generateCid(data) {
    return axios.post('/api/cid/generate', data)
      .then(response => response.data)
  },

  /**
   * 解析番号
   * @param {Object} data - 解析参数
   * @param {string} data.bangou - 番号
   */
  parseBangou(data) {
    return axios.post('/api/cid/parse-bangou', data)
      .then(response => response.data)
  },

  /**
   * AVBase查询CID并推导规则
   * @param {Object} data - 查询参数
   * @param {string} data.bangou - 番号
   */
  avbaseLookup(data) {
    return axios.post('/api/cid/avbase-lookup', data)
      .then(response => response.data)
  },

  /**
   * 从番号,CID字符串解析规则
   * @param {Object} data - 解析参数
   * @param {string} data.text - 解析文本（格式：番号,CID）
   */
  parseRule(data) {
    return axios.post('/api/cid/parse-rule', data)
      .then(response => response.data)
  },

  /**
   * 批量导入主规则
   * @param {Object} data - 导入参数
   * @param {string} data.rules_text - 规则文本（每行一个规则）
   */
  batchImportRules(data) {
    return axios.post('/api/cid/batch-import', data)
      .then(response => response.data)
  },

  /**
   * 强制执行WAL检查点
   */
  forceCheckpoint() {
    return axios.post('/api/cid/force-checkpoint')
      .then(response => response.data)
  }
}

export default cidApi
