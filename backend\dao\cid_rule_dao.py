# backend/dao/cid_rule_dao.py
"""
CID规则数据访问对象
"""
import logging
from typing import List, Dict, Any, Optional
from dao.base_dao import BaseDAO
from db_context import db_context

logger = logging.getLogger(__name__)

# 表创建逻辑已移至 api/cid_api.py 中

class CidRuleDAO(BaseDAO):
    """CID主规则数据访问对象"""

    def __init__(self):
        super().__init__('cid_conversion_rules')

    def get_table_schema(self) -> Dict[str, str]:
        """获取CID主规则表结构定义"""
        return {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'series_name': 'TEXT NOT NULL UNIQUE',
            'cid_prefix': 'TEXT',
            'series_name_in_cid': 'TEXT',
            'number_padding': 'INTEGER NOT NULL',
            'suffix': 'TEXT',
            'description': 'TEXT',
            'status': 'TEXT DEFAULT "已确认"',
            'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
            'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP'
        }
    
    def find_by_series_name(self, series_name: str) -> Optional[Dict[str, Any]]:
        """
        根据番号前缀查找主规则

        Args:
            series_name: 番号前缀

        Returns:
            主规则字典或None
        """
        query = f"SELECT * FROM {self.table_name} WHERE series_name = ?"
        result = db_context.execute_query(query, (series_name,), fetch_one=True)
        return dict(result) if result else None
    
    def find_all_with_variant_count(self, search: str = '', limit: Optional[int] = None,
                                   offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取所有主规则，包含变体规则数量

        Args:
            search: 搜索关键词
            limit: 限制返回记录数
            offset: 偏移量

        Returns:
            主规则列表，每个规则包含variant_count字段
        """
        where_clause = ""
        params = []

        if search:
            where_clause = "WHERE m.series_name LIKE ?"
            params.append(f'%{search}%')

        query = f"""
        SELECT m.*, COUNT(v.id) as variant_count
        FROM {self.table_name} m
        LEFT JOIN cid_variant_rules v ON m.id = v.main_rule_id
        {where_clause}
        GROUP BY m.id
        ORDER BY m.series_name
        """

        if limit is not None:
            query += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])

        results = db_context.execute_query(query, tuple(params))
        return [dict(row) for row in results] if results else []
    
    def count_with_search(self, search: str = '') -> int:
        """
        获取主规则总数（支持搜索）
        
        Args:
            search: 搜索关键词
            
        Returns:
            记录总数
        """
        where_clause = ""
        params = []
        
        if search:
            where_clause = "WHERE series_name LIKE ?"
            params.append(f'%{search}%')
        
        query = f"SELECT COUNT(*) as count FROM {self.table_name} {where_clause}"
        result = db_context.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] if result else 0

class CidVariantDAO(BaseDAO):
    """CID变体规则数据访问对象"""

    def __init__(self):
        super().__init__('cid_variant_rules')

    def get_table_schema(self) -> Dict[str, str]:
        """获取CID变体规则表结构定义"""
        return {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'main_rule_id': 'INTEGER NOT NULL',
            'maker': 'TEXT',
            'serial_number_range': 'TEXT',
            'cid_prefix': 'TEXT',
            'series_name_in_cid': 'TEXT',
            'number_padding': 'INTEGER NOT NULL',
            'suffix': 'TEXT',
            'priority': 'INTEGER DEFAULT 0',
            'description': 'TEXT',
            'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
            'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP'
        }
    
    def find_by_main_rule_id(self, main_rule_id: int, maker: str = None) -> List[Dict[str, Any]]:
        """
        根据主规则ID查找变体规则
        
        Args:
            main_rule_id: 主规则ID
            maker: 制作商（可选）
            
        Returns:
            变体规则列表
        """
        if maker:
            query = f"SELECT * FROM {self.table_name} WHERE main_rule_id = ? AND maker = ? ORDER BY priority ASC"
            params = (main_rule_id, maker)
        else:
            query = f"SELECT * FROM {self.table_name} WHERE main_rule_id = ? ORDER BY priority ASC"
            params = (main_rule_id,)
        
        results = db_context.execute_query(query, params)
        return [dict(row) for row in results] if results else []
    
    def update_priorities(self, variant_ids_with_priorities: List[Dict[str, int]]) -> bool:
        """
        批量更新变体规则优先级 - 安全版本

        Args:
            variant_ids_with_priorities: [{"id": 1, "priority": 1}, ...]

        Returns:
            是否更新成功
        """
        try:
            self.logger.info(f"开始更新 {len(variant_ids_with_priorities)} 个变体规则的优先级")

            # 逐个更新，使用最安全的方式
            updated_count = 0
            for item in variant_ids_with_priorities:
                try:
                    # 使用BaseDAO的update方法，更安全
                    success = self.update(item['id'], {'priority': item['priority']})
                    if success:
                        updated_count += 1
                        self.logger.debug(f"成功更新规则 {item['id']} 的优先级为 {item['priority']}")
                    else:
                        self.logger.warning(f"更新规则 {item['id']} 的优先级失败")
                except Exception as e:
                    self.logger.error(f"更新规则 {item['id']} 时出错: {e}")
                    # 继续处理其他规则，不中断整个过程
                    continue

            self.logger.info(f"优先级更新完成: 成功更新 {updated_count}/{len(variant_ids_with_priorities)} 个规则")
            return updated_count > 0

        except Exception as e:
            self.logger.error(f"批量更新变体规则优先级失败: {e}")
            return False
    
    def get_max_priority(self, main_rule_id: int) -> int:
        """
        获取指定主规则下变体规则的最大优先级
        
        Args:
            main_rule_id: 主规则ID
            
        Returns:
            最大优先级值
        """
        query = f"SELECT MAX(priority) as max_priority FROM {self.table_name} WHERE main_rule_id = ?"
        result = db_context.execute_query(query, (main_rule_id,), fetch_one=True)
        return result['max_priority'] if result and result['max_priority'] is not None else -1

    def fix_priorities(self, main_rule_id: int) -> bool:
        """
        修复指定主规则下变体规则的优先级顺序
        将所有变体规则的优先级重新排序为 0, 1, 2, ...

        Args:
            main_rule_id: 主规则ID

        Returns:
            是否修复成功
        """
        try:
            # 获取所有变体规则，按当前优先级排序
            variants = self.find_by_main_rule_id(main_rule_id)

            if not variants:
                self.logger.info(f"主规则 {main_rule_id} 下没有变体规则需要修复")
                return True

            self.logger.info(f"开始修复主规则 {main_rule_id} 下 {len(variants)} 个变体规则的优先级")

            # 重新分配优先级
            fixed_count = 0
            for index, variant in enumerate(variants):
                new_priority = index
                if variant.get('priority') != new_priority:
                    success = self.update(variant['id'], {'priority': new_priority})
                    if success:
                        fixed_count += 1
                        self.logger.debug(f"修复规则 {variant['id']} 优先级: {variant.get('priority')} -> {new_priority}")

            self.logger.info(f"优先级修复完成: 修复了 {fixed_count} 个规则")
            return True

        except Exception as e:
            self.logger.error(f"修复优先级失败: {e}")
            return False

# 创建DAO实例
cid_rule_dao = CidRuleDAO()
cid_variant_dao = CidVariantDAO()
