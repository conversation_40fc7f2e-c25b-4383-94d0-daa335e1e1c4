# backend/services/cid_service.py
"""
CID业务逻辑服务
"""
import re
import logging
from typing import Dict, Any, Optional, List
from dao.cid_rule_dao import cid_rule_dao, cid_variant_dao

logger = logging.getLogger(__name__)

# 番号正则表达式
BANGOU_REGEX = re.compile(r'^([A-Z]+)-?(\d+)$')

class CidService:
    """CID服务类"""
    
    def __init__(self):
        self.logger = logger
    
    def get_applicable_cid_rule(self, series_name: str, num_part: str, maker: str = None) -> Dict[str, Any]:
        """
        根据番号前缀、编号和制作商获取适用的CID规则
        
        匹配逻辑：
        1. 查找番号前缀对应的主规则
        2. 如果有maker信息，查找该主规则下的变体规则
        3. 按优先级遍历变体规则，检查maker和范围匹配
        4. 如果都不匹配，返回主规则作为保底
        
        Args:
            series_name: 番号前缀（如SW）
            num_part: 编号部分（如123）
            maker: 制作商（NFO中的maker字段）
        
        Returns:
            匹配的规则字典
        """
        
        # 1. 查找主规则
        main_rule = cid_rule_dao.find_by_series_name(series_name)
        if not main_rule:
            raise ValueError(f"未找到番号前缀 '{series_name}' 的主规则")
        
        # 2. 查找变体规则，按优先级排序
        variant_rules = cid_variant_dao.find_by_main_rule_id(main_rule['id'])

        # 3. 遍历变体规则，检查匹配条件
        for rule in variant_rules:
            # 检查制作商匹配逻辑
            maker_enabled = rule.get('maker_enabled', 0)

            if maker_enabled:
                # 制作商开关打开：必须有制作商且匹配
                if not maker or not rule.get('maker') or rule['maker'] != maker:
                    continue
            else:
                # 制作商开关关闭：忽略制作商，只检查范围
                pass

            # 检查范围匹配
            if self._evaluate_range(num_part, rule['serial_number_range']):
                return {
                    'rule': rule,
                    'rule_type': 'variant',
                    'rule_info': f"变体规则: {rule['description'] or '无描述'}",
                    'main_rule_id': main_rule['id']
                }
        
        # 4. 返回主规则作为保底
        return {
            'rule': main_rule,
            'rule_type': 'main',
            'rule_info': f"主规则: {main_rule['description']}",
            'main_rule_id': main_rule['id']
        }
    
    def _evaluate_range(self, num_part: str, range_condition: str) -> bool:
        """
        评估编号是否符合范围条件

        支持的范围格式：
        - 'xxx': 具体范围标识符
        - '>=100': 大于等于100
        - '<=500': 小于等于500
        - '1-500': 1到500之间
        - '100,200,300': 特定数值列表
        - '529, 540, 581, 641-642, 676': 混合格式（单个数字和范围）
        """
        if not range_condition:
            return True

        # 清理空格
        range_condition = range_condition.strip()

        # 如果是具体的范围标识符（如xxx, yyy, zzz等）
        if range_condition.isalpha():
            # 这里需要根据实际业务逻辑来判断
            # 暂时返回False，需要具体的映射规则
            self.logger.warning(f"遇到字母范围条件 '{range_condition}'，需要具体的映射规则")
            return False

        try:
            num = int(num_part)

            # 处理简单的比较操作
            if '>=' in range_condition:
                threshold = int(range_condition.replace('>=', '').strip())
                return num >= threshold
            elif '<=' in range_condition:
                threshold = int(range_condition.replace('<=', '').strip())
                return num <= threshold

            # 处理混合格式：包含逗号分隔的多个条件
            if ',' in range_condition:
                conditions = [cond.strip() for cond in range_condition.split(',')]
                for condition in conditions:
                    if self._evaluate_single_condition(num, condition):
                        return True
                return False
            else:
                # 处理单个条件
                return self._evaluate_single_condition(num, range_condition)

        except ValueError as e:
            self.logger.error(f"无法解析范围条件: {range_condition}, 错误: {e}")
            return False

        return False

    def _evaluate_single_condition(self, num: int, condition: str) -> bool:
        """
        评估单个条件

        Args:
            num: 要检查的数字
            condition: 单个条件（如 "529", "641-642"）

        Returns:
            是否匹配条件
        """
        condition = condition.strip()

        try:
            # 处理范围条件（如 641-642）
            if '-' in condition and not condition.startswith('-'):
                parts = condition.split('-')
                if len(parts) == 2:
                    start, end = int(parts[0].strip()), int(parts[1].strip())
                    return start <= num <= end

            # 处理单个数字
            target_num = int(condition)
            return num == target_num

        except ValueError:
            self.logger.error(f"无法解析单个条件: {condition}")
            return False
    
    def generate_cid(self, bangou: str, maker: str = None) -> Dict[str, Any]:
        """
        根据番号和制作商生成CID
        
        Args:
            bangou: 番号（如SW-123）
            maker: 制作商（可选）
            
        Returns:
            生成结果字典
        """
        bangou = bangou.upper().strip()
        if not bangou:
            raise ValueError("番号不能为空")
        
        match = BANGOU_REGEX.match(bangou)
        if not match:
            raise ValueError("无法解析番号格式")
        
        series_name, num_part = match.groups()
        
        # 获取适用的规则
        rule_result = self.get_applicable_cid_rule(series_name, num_part, maker)
        rule = rule_result['rule']
        
        # 生成CID
        if rule['number_padding'] == 0:
            padded_num = num_part  # 保持原编号
        else:
            padded_num = num_part.zfill(rule['number_padding'])  # 补位
        
        cid_parts = []
        if rule.get('cid_prefix'):
            cid_parts.append(rule['cid_prefix'])
        if rule.get('series_name_in_cid'):
            cid_parts.append(rule['series_name_in_cid'])
        cid_parts.append(padded_num)
        if rule.get('suffix'):
            cid_parts.append(rule['suffix'])
        
        cid = ''.join(cid_parts)
        
        return {
            "success": True,
            "bangou": bangou,
            "cid": cid,
            "rule_info": rule_result['rule_info'],
            "rule_type": rule_result['rule_type'],
            "maker_used": maker if maker else "未提供制作商信息",
            "dmm_digital_url": f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/",
            "dmm_mono_url": f"https://www.dmm.co.jp/mono/dvd/-/detail/=/cid={cid}/"
        }
    
    def parse_bangou(self, bangou: str) -> Dict[str, str]:
        """
        解析番号
        
        Args:
            bangou: 番号
            
        Returns:
            解析结果 {"series_name": "SW", "num_part": "123"}
        """
        bangou = bangou.upper().strip()
        match = BANGOU_REGEX.match(bangou)
        if not match:
            raise ValueError("无法解析番号格式")
        
        series_name, num_part = match.groups()
        return {
            "series_name": series_name,
            "num_part": num_part
        }

    def _generate_cid_with_rule(self, bangou: str, rule: dict) -> str:
        """
        使用指定规则生成CID

        Args:
            bangou: 番号
            rule: 规则字典，包含prefix, series_name_in_cid, padding, suffix

        Returns:
            生成的CID
        """
        # 提取番号中的数字部分
        match = re.search(BANGOU_REGEX, bangou.upper())
        if not match:
            raise ValueError(f"无法解析番号格式: {bangou}")

        num_part = match.group(2)

        # 应用规则生成CID
        cid_prefix = rule.get('prefix', '')
        series_name_in_cid = rule.get('series_name_in_cid', '')
        padding = rule.get('padding', 0)
        suffix = rule.get('suffix', '')

        # 处理数字补位
        if padding > 0:
            num_part = num_part.zfill(padding)

        # 组合CID
        cid = f"{cid_prefix}{series_name_in_cid}{num_part}{suffix}"
        return cid

# 创建服务实例
cid_service = CidService()
