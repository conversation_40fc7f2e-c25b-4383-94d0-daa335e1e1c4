# WAL检查点问题解决方案

## 问题描述

CID规则写入后查询显示已有规则数，但重启后规则数就没了，WAL文件并没有丢失。

## 问题原因

这是SQLite WAL（Write-Ahead Logging）模式的特性导致的：

1. **WAL模式工作原理**：写操作先写入WAL文件，读操作从WAL文件和主数据库文件合并读取
2. **检查点机制**：只有执行检查点（checkpoint）时，WAL文件中的数据才会写入主数据库文件
3. **自动检查点设置**：原设置为1000次写操作才触发自动检查点，导致数据可能长时间只存在于WAL文件中
4. **重启问题**：如果在检查点执行前重启，WAL文件中的数据可能无法正确恢复

## 解决方案

### 1. 降低自动检查点阈值

将WAL自动检查点从1000次降低到100次写操作：

```sql
PRAGMA wal_autocheckpoint = 100;
```

### 2. 连接池返回时执行检查点

每次数据库连接返回连接池时自动执行被动检查点：

```sql
PRAGMA wal_checkpoint(PASSIVE);
```

### 3. 关键操作后强制检查点

在CID规则的创建、更新、批量导入操作后强制执行检查点：

```sql
PRAGMA wal_checkpoint(FULL);
```

### 4. 定期检查点服务

启动后台服务，每5分钟自动执行一次检查点，确保数据及时持久化。

### 5. 启动时检查点

应用启动时执行一次检查点，确保之前未持久化的数据得到处理。

## 使用方法

### 手动执行检查点

1. **前端界面**：在CID管理页面点击"强制保存"按钮
2. **API调用**：
   ```bash
   curl -X POST http://localhost:5000/api/cid/force-checkpoint
   ```

### 命令行工具

使用数据库维护工具：

```bash
# 检查WAL状态
python backend/tools/db_maintenance.py --status

# 强制执行检查点
python backend/tools/db_maintenance.py --checkpoint

# 执行所有维护操作
python backend/tools/db_maintenance.py --all

# 数据库完整性检查
python backend/tools/db_maintenance.py --integrity
```

### 检查服务状态

```bash
curl http://localhost:5000/api/cid/checkpoint-status
```

## 验证方法

1. **创建规则后立即重启**：
   - 创建CID规则
   - 点击"强制保存"按钮
   - 重启应用
   - 检查规则是否仍然存在

2. **检查WAL文件大小**：
   ```bash
   ls -la db/media.db*
   ```
   正常情况下WAL文件应该很小或不存在

3. **查看日志**：
   检查应用日志中的检查点执行记录

## 最佳实践

1. **重要操作后手动保存**：创建或修改重要规则后，点击"强制保存"
2. **定期检查WAL状态**：使用维护工具定期检查数据库状态
3. **备份策略**：定期备份主数据库文件（media.db）
4. **监控日志**：关注检查点执行的日志记录

## 技术细节

### WAL模式优势
- 提高并发性能
- 读写不互相阻塞
- 更好的崩溃恢复

### 检查点类型
- **PASSIVE**：不阻塞其他操作，尽力而为
- **FULL**：等待所有读者完成，确保完全同步
- **RESTART**：重置WAL文件

### 配置参数
- `wal_autocheckpoint`：自动检查点阈值
- `synchronous`：同步级别
- `journal_mode`：日志模式

## 故障排除

### 如果规则仍然丢失

1. 检查数据库文件权限
2. 检查磁盘空间
3. 查看应用错误日志
4. 使用完整性检查工具
5. 考虑从备份恢复

### 性能影响

- 频繁检查点可能略微影响写入性能
- 但能确保数据安全性
- 可根据实际情况调整检查点频率

## 监控建议

1. 监控WAL文件大小
2. 监控检查点执行频率
3. 监控数据库操作错误
4. 定期验证数据完整性
