# backend/services/checkpoint_service.py
"""
WAL检查点服务
定期执行WAL检查点，确保数据持久化
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from db_manager import force_wal_checkpoint

logger = logging.getLogger(__name__)

class CheckpointService:
    """WAL检查点服务"""
    
    def __init__(self, interval_minutes=5):
        """
        初始化检查点服务
        
        Args:
            interval_minutes: 检查点执行间隔（分钟）
        """
        self.interval_minutes = interval_minutes
        self.interval_seconds = interval_minutes * 60
        self.running = False
        self.thread = None
        self.last_checkpoint = None
        self.checkpoint_count = 0
        self.failed_count = 0
        
    def start(self):
        """启动检查点服务"""
        if self.running:
            logger.warning("检查点服务已在运行")
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_checkpoint_loop, daemon=True)
        self.thread.start()
        logger.info(f"检查点服务已启动，间隔: {self.interval_minutes}分钟")
        
    def stop(self):
        """停止检查点服务"""
        if not self.running:
            return
            
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("检查点服务已停止")
        
    def _run_checkpoint_loop(self):
        """检查点循环"""
        while self.running:
            try:
                # 执行检查点
                success = force_wal_checkpoint()
                
                if success:
                    self.checkpoint_count += 1
                    self.last_checkpoint = datetime.now()
                    logger.debug(f"定期检查点执行成功 (第{self.checkpoint_count}次)")
                else:
                    self.failed_count += 1
                    logger.warning(f"定期检查点执行失败 (失败次数: {self.failed_count})")
                    
            except Exception as e:
                self.failed_count += 1
                logger.error(f"检查点服务异常: {e}")
                
            # 等待下一次执行
            for _ in range(self.interval_seconds):
                if not self.running:
                    break
                time.sleep(1)
                
    def get_status(self):
        """获取服务状态"""
        return {
            'running': self.running,
            'interval_minutes': self.interval_minutes,
            'last_checkpoint': self.last_checkpoint.isoformat() if self.last_checkpoint else None,
            'checkpoint_count': self.checkpoint_count,
            'failed_count': self.failed_count,
            'next_checkpoint_in_seconds': self._get_next_checkpoint_seconds()
        }
        
    def _get_next_checkpoint_seconds(self):
        """获取下次检查点的剩余秒数"""
        if not self.running or not self.last_checkpoint:
            return 0
            
        next_checkpoint = self.last_checkpoint + timedelta(minutes=self.interval_minutes)
        remaining = (next_checkpoint - datetime.now()).total_seconds()
        return max(0, int(remaining))
        
    def force_checkpoint_now(self):
        """立即执行检查点"""
        try:
            success = force_wal_checkpoint()
            if success:
                self.checkpoint_count += 1
                self.last_checkpoint = datetime.now()
                logger.info("手动检查点执行成功")
                return True
            else:
                self.failed_count += 1
                logger.warning("手动检查点执行失败")
                return False
        except Exception as e:
            self.failed_count += 1
            logger.error(f"手动检查点执行异常: {e}")
            return False

# 全局检查点服务实例
checkpoint_service = CheckpointService(interval_minutes=5)
