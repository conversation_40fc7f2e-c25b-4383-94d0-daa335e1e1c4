#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库维护工具
用于执行WAL检查点、数据库优化等维护操作
"""

import os
import sys
import sqlite3
import argparse
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_manager import DB_PATH, force_wal_checkpoint, get_db_connection, return_connection_to_pool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_maintenance.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_wal_status():
    """检查WAL文件状态"""
    try:
        conn = get_db_connection()
        try:
            # 检查WAL模式
            result = conn.execute("PRAGMA journal_mode").fetchone()
            journal_mode = result[0] if result else "unknown"
            
            # 检查WAL文件大小
            wal_file = DB_PATH + '-wal'
            wal_size = os.path.getsize(wal_file) if os.path.exists(wal_file) else 0
            
            # 检查SHM文件大小
            shm_file = DB_PATH + '-shm'
            shm_size = os.path.getsize(shm_file) if os.path.exists(shm_file) else 0
            
            # 获取WAL检查点信息
            checkpoint_info = conn.execute("PRAGMA wal_checkpoint").fetchone()
            
            logger.info(f"数据库文件: {DB_PATH}")
            logger.info(f"Journal模式: {journal_mode}")
            logger.info(f"WAL文件大小: {wal_size} 字节")
            logger.info(f"SHM文件大小: {shm_size} 字节")
            logger.info(f"检查点信息: {checkpoint_info}")
            
            return {
                'journal_mode': journal_mode,
                'wal_size': wal_size,
                'shm_size': shm_size,
                'checkpoint_info': checkpoint_info
            }
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"检查WAL状态失败: {e}")
        return None

def force_checkpoint():
    """强制执行WAL检查点"""
    logger.info("开始执行强制WAL检查点...")
    try:
        conn = get_db_connection()
        try:
            # 执行完整的WAL检查点
            result = conn.execute("PRAGMA wal_checkpoint(FULL)").fetchone()
            logger.info(f"WAL检查点执行完成: {result}")
            
            # 再次检查WAL文件状态
            wal_file = DB_PATH + '-wal'
            wal_size_after = os.path.getsize(wal_file) if os.path.exists(wal_file) else 0
            logger.info(f"检查点后WAL文件大小: {wal_size_after} 字节")
            
            return True
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"强制WAL检查点失败: {e}")
        return False

def vacuum_database():
    """执行数据库VACUUM操作"""
    logger.info("开始执行数据库VACUUM操作...")
    try:
        conn = get_db_connection()
        try:
            # 先执行检查点确保所有数据都在主数据库文件中
            conn.execute("PRAGMA wal_checkpoint(FULL)")
            
            # 执行VACUUM
            conn.execute("VACUUM")
            logger.info("数据库VACUUM操作完成")
            
            return True
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"数据库VACUUM操作失败: {e}")
        return False

def analyze_database():
    """分析数据库统计信息"""
    logger.info("开始分析数据库...")
    try:
        conn = get_db_connection()
        try:
            # 更新统计信息
            conn.execute("ANALYZE")
            logger.info("数据库分析完成")
            
            return True
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"数据库分析失败: {e}")
        return False

def check_integrity():
    """检查数据库完整性"""
    logger.info("开始检查数据库完整性...")
    try:
        conn = get_db_connection()
        try:
            # 执行完整性检查
            result = conn.execute("PRAGMA integrity_check").fetchall()
            
            if len(result) == 1 and result[0][0] == 'ok':
                logger.info("数据库完整性检查通过")
                return True
            else:
                logger.error(f"数据库完整性检查失败: {result}")
                return False
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"数据库完整性检查失败: {e}")
        return False

def get_table_stats():
    """获取表统计信息"""
    logger.info("获取表统计信息...")
    try:
        conn = get_db_connection()
        try:
            # 获取所有表名
            tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
            
            stats = {}
            for table in tables:
                table_name = table[0]
                count = conn.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
                stats[table_name] = count
                logger.info(f"表 {table_name}: {count} 条记录")
            
            return stats
        finally:
            return_connection_to_pool(conn)
    except Exception as e:
        logger.error(f"获取表统计信息失败: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='数据库维护工具')
    parser.add_argument('--status', action='store_true', help='检查WAL状态')
    parser.add_argument('--checkpoint', action='store_true', help='强制执行WAL检查点')
    parser.add_argument('--vacuum', action='store_true', help='执行数据库VACUUM')
    parser.add_argument('--analyze', action='store_true', help='分析数据库统计信息')
    parser.add_argument('--integrity', action='store_true', help='检查数据库完整性')
    parser.add_argument('--stats', action='store_true', help='获取表统计信息')
    parser.add_argument('--all', action='store_true', help='执行所有维护操作')
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    logger.info(f"数据库维护工具启动 - {datetime.now()}")
    
    if args.status or args.all:
        check_wal_status()
    
    if args.checkpoint or args.all:
        force_checkpoint()
    
    if args.integrity or args.all:
        check_integrity()
    
    if args.stats or args.all:
        get_table_stats()
    
    if args.analyze or args.all:
        analyze_database()
    
    if args.vacuum:  # VACUUM不包含在--all中，因为它可能很耗时
        vacuum_database()
    
    logger.info("数据库维护完成")

if __name__ == '__main__':
    main()
